#!/bin/bash

# SahAI CEP Extension Installation Script for macOS
# This script installs the extension in the correct CEP directory

echo "🔧 Installing SahAI CEP Extension..."

# CEP extension directories for macOS
CEP_DIR="$HOME/Library/Application Support/Adobe/CEP/extensions"
EXTENSION_NAME="com.sahai.cep"
EXTENSION_PATH="$CEP_DIR/$EXTENSION_NAME"

# Create CEP directory if it doesn't exist
mkdir -p "$CEP_DIR"

# Remove existing extension if it exists
if [ -d "$EXTENSION_PATH" ]; then
    echo "🗑️  Removing existing extension..."
    rm -rf "$EXTENSION_PATH"
fi

# Copy the extension
echo "📦 Copying extension files..."
cp -r dist "$EXTENSION_PATH"

# Enable debug mode for CEP
echo "🔍 Enabling CEP debug mode..."
defaults write com.adobe.CSXS.11 PlayerDebugMode 1
defaults write com.adobe.CSXS.10 PlayerDebugMode 1
defaults write com.adobe.CSXS.9 PlayerDebugMode 1

echo "✅ Extension installed successfully!"
echo "📍 Location: $EXTENSION_PATH"
echo ""
echo "🔄 Please restart After Effects"
echo "📋 Look for 'SahAI Chat Bot' in Window > Extensions"
echo ""
echo "🔍 Debug logs will appear in After Effects' ExtendScript Toolkit console"
