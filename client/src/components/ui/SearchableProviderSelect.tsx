import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Search, ChevronDown } from 'lucide-react';

interface ProviderOption {
  id: string;
  name: string;
  isConfigured?: boolean;
}

interface SearchableProviderSelectProps {
  providers: ProviderOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SearchableProviderSelect: React.FC<SearchableProviderSelectProps> = ({
  providers,
  value,
  onChange,
  placeholder = "Search providers..."
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(0);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredProviders = providers.filter(provider =>
    provider.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    provider.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedProvider = providers.find(p => p.id === value);

  const handleProviderSelect = useCallback((providerId: string) => {
    onChange(providerId);
    setShowDropdown(false);
    setSearchQuery('');
    setFocusedIndex(0);
  }, [onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showDropdown && (e.key === 'ArrowDown' || e.key === 'ArrowUp')) {
      e.preventDefault();
      setShowDropdown(true);
      setSearchQuery('');
      return;
    }

    if (!showDropdown) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev < filteredProviders.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredProviders[focusedIndex]) {
          handleProviderSelect(filteredProviders[focusedIndex].id);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowDropdown(false);
        setFocusedIndex(0);
        break;
    }
  }, [showDropdown, filteredProviders, focusedIndex, handleProviderSelect]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
        setFocusedIndex(0);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    setFocusedIndex(0);
  }, [filteredProviders]);

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={showDropdown ? searchQuery : (selectedProvider?.name || '')}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setShowDropdown(true);
            setFocusedIndex(0);
          }}
          onFocus={() => {
            setShowDropdown(true);
            setSearchQuery('');
            setFocusedIndex(0);
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-2 focus:ring-adobe-accent/20 outline-none transition-all pr-10"
          readOnly={!showDropdown}
        />
        <button
          onClick={() => {
            if (showDropdown && filteredProviders.length > 0) {
              handleProviderSelect(filteredProviders[focusedIndex]?.id || filteredProviders[0].id);
            } else {
              setShowDropdown(true);
              setSearchQuery('');
              setFocusedIndex(0);
            }
          }}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
        >
          <ChevronDown size={18} className={`transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
        </button>
      </div>

      {showDropdown && (
        <div className="absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-lg shadow-lg max-h-52 overflow-auto">
          {filteredProviders.length > 0 ? (
            filteredProviders.map((provider, index) => (
              <div
                key={provider.id}
                className={`px-4 py-3 cursor-pointer flex items-center justify-between ${
                  value === provider.id
                    ? 'bg-adobe-accent text-white'
                    : index === focusedIndex
                    ? 'bg-adobe-bg-tertiary text-adobe-text-primary'
                    : 'hover:bg-adobe-bg-tertiary text-adobe-text-primary'
                }`}
                onClick={() => handleProviderSelect(provider.id)}
                onMouseEnter={() => setFocusedIndex(index)}
              >
                <span>{provider.name}</span>
                {provider.isConfigured && (
                  <div className={`w-2 h-2 rounded-full ${
                    value === provider.id ? 'bg-white' : 'bg-green-500'
                  }`} />
                )}
              </div>
            ))
          ) : (
            <div className="px-4 py-3 text-adobe-text-secondary">
              No providers found
            </div>
          )}
        </div>
      )}
    </div>
  );
};
