import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Search, ChevronDown } from 'lucide-react';

interface ModelOption {
  id: string;
  name: string;
}

interface SearchableModelSelectProps {
  models: ModelOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SearchableModelSelect: React.FC<SearchableModelSelectProps> = ({
  models,
  value,
  onChange,
  placeholder = "Search models..."
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(0);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedModel = models.find(m => m.id === value);

  const handleModelSelect = useCallback((modelId: string) => {
    onChange(modelId);
    setShowDropdown(false);
    setSearchQuery('');
    setFocusedIndex(0);
  }, [onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showDropdown && (e.key === 'ArrowDown' || e.key === 'ArrowUp')) {
      e.preventDefault();
      setShowDropdown(true);
      setSearchQuery('');
      return;
    }

    if (!showDropdown) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev < filteredModels.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredModels[focusedIndex]) {
          handleModelSelect(filteredModels[focusedIndex].id);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setShowDropdown(false);
        setFocusedIndex(0);
        break;
    }
  }, [showDropdown, filteredModels, focusedIndex, handleModelSelect]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
        setFocusedIndex(0);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    setFocusedIndex(0);
  }, [filteredModels]);

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={showDropdown ? searchQuery : (selectedModel?.name || '')}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setShowDropdown(true);
            setFocusedIndex(0);
          }}
          onFocus={() => {
            setShowDropdown(true);
            setSearchQuery('');
            setFocusedIndex(0);
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10"
          readOnly={!showDropdown}
        />
        <button
          onClick={() => {
            if (showDropdown && filteredModels.length > 0) {
              handleModelSelect(filteredModels[focusedIndex]?.id || filteredModels[0].id);
            } else {
              setShowDropdown(true);
              setSearchQuery('');
              setFocusedIndex(0);
            }
          }}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary"
        >
          <Search size={18} />
        </button>
      </div>

      {showDropdown && (
        <div className="absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-lg max-h-52 overflow-auto">
          {filteredModels.length > 0 ? (
            filteredModels.map((model, index) => (
              <div
                key={model.id}
                className={`px-4 py-2 cursor-pointer ${
                  value === model.id
                    ? 'bg-adobe-accent text-white'
                    : index === focusedIndex
                    ? 'bg-adobe-bg-tertiary text-adobe-text-primary'
                    : 'hover:bg-adobe-bg-tertiary text-adobe-text-primary'
                }`}
                onClick={() => handleModelSelect(model.id)}
                onMouseEnter={() => setFocusedIndex(index)}
              >
                {model.name}
              </div>
            ))
          ) : (
            <div className="px-4 py-2 text-adobe-text-secondary">
              No models found
            </div>
          )}
        </div>
      )}
    </div>
  );
};
