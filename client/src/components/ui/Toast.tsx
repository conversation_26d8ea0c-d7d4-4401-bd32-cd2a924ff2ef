import React, { useEffect, useState } from 'react';
import { useToastStore, Toast as ToastType } from '../stores/toastStore';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

interface ToastProps {
  toast: ToastType;
}

const ToastItem: React.FC<ToastProps> = ({ toast }) => {
  const { removeToast } = useToastStore();
  const [isExiting, setIsExiting] = useState(false);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => removeToast(toast.id), 200); // Match animation duration
  };

  useEffect(() => {
    // Auto-close after duration
    const timer = setTimeout(() => {
      handleClose();
    }, toast.duration || 4000);

    return () => clearTimeout(timer);
  }, [toast.duration]);

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle size={20} className="text-adobe-success" />;
      case 'error':
        return <AlertCircle size={20} className="text-adobe-error" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-adobe-warning" />;
      case 'info':
      default:
        return <Info size={20} className="text-adobe-accent" />;
    }
  };

  const getBorderColor = () => {
    switch (toast.type) {
      case 'success':
        return 'border-l-adobe-success';
      case 'error':
        return 'border-l-adobe-error';
      case 'warning':
        return 'border-l-adobe-warning';
      case 'info':
      default:
        return 'border-l-adobe-accent';
    }
  };

  return (
    <div
      className={`
        transform transition-all duration-200 ease-in-out
        ${isExiting ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'}
        bg-adobe-bg-secondary border border-adobe-border ${getBorderColor()} border-l-4
        rounded-md shadow-lg p-4 mb-3 max-w-sm w-full
      `}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-adobe-text-primary truncate">
            {toast.title}
          </h4>
          {toast.message && (
            <p className="text-xs text-adobe-text-secondary mt-1 break-words">
              {toast.message}
            </p>
          )}
        </div>
        <button
          onClick={handleClose}
          className="flex-shrink-0 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export const ToastContainer: React.FC = () => {
  const { toasts } = useToastStore();

  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-[9999] pointer-events-none">
      <div className="space-y-2 pointer-events-auto">
        {toasts.map(toast => (
          <ToastItem key={toast.id} toast={toast} />
        ))}
      </div>
    </div>
  );
};
