import React, { useEffect, useState } from 'react';
import { Copy, Download } from 'lucide-react';
import type { Highlighter } from 'shiki';
import { getOptimizedHighlighter, isSupportedLanguage, getFallbackLanguage } from '../../utils/shiki-setup';

export const ShikiCodeBlock: React.FC<{ content: string }> = ({ content }) => {
  const [highlighter, setHighlighter] = useState<Highlighter | null>(null);

  useEffect(() => {
    // Use our optimized highlighter setup
    getOptimizedHighlighter().then(setHighlighter);
  }, []);

  if (!highlighter) return <pre className="whitespace-pre-wrap">{content}</pre>;

  // Split content into code blocks and regular text
  const parts = content.split(/(```[\s\S]*?```)/g);

  return (
    <>
      {parts.map((part, idx) => {
        if (part.startsWith('```')) {
          const lines = part.split('\n');
          const rawLang = lines[0].replace('```', '').trim();
          const code = lines.slice(1, -1).join('\n');

          // Use fallback language if not supported
          const lang = isSupportedLanguage(rawLang) ? rawLang : getFallbackLanguage(rawLang);

          return (
            <div key={idx} className="relative">
              <div className="absolute top-1 right-1 flex gap-1">
                <button
                  title="Copy"
                  onClick={() => navigator.clipboard.writeText(code)}
                  className="p-1 bg-adobe-bg-primary/80 rounded text-xs"
                >
                  <Copy size={14} />
                </button>
                <button
                  title="Save"
                  className="p-1 bg-adobe-bg-primary/80 rounded text-xs"
                >
                  <Download size={14} />
                </button>
              </div>
              <div
                dangerouslySetInnerHTML={{
                  __html: highlighter.codeToHtml(code, {
                    lang,
                    theme: 'github-dark',
                  }),
                }}
              />
            </div>
          );
        }
        return <div key={idx}>{part}</div>;
      })}
    </>
  );
};