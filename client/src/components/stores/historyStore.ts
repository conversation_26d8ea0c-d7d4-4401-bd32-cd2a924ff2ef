import { create } from 'zustand';
import { executeExtendScript } from '../../utils/cepIntegration';
import { HISTORY_STORAGE_KEY } from '../../utils/constants';

export interface ChatSession {
  id: string;
  title: string;
  messages: Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  createdAt: number;
  updatedAt: number;
  provider?: string;
  model?: string;
  tokenCount?: number;
  cost?: number;
  avgLatency?: number;
}

interface HistoryState {
  sessions: ChatSession[];
  currentSessionId: string | null;
  isLoading: boolean;
  error: string | null;

  // Session management
  loadHistory: () => Promise<void>;
  saveSession: (session: ChatSession) => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  clearHistory: () => Promise<void>;
  
  // Session operations
  createSession: (title?: string) => ChatSession;
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => void;
  setCurrentSession: (sessionId: string | null) => void;
  
  // Computed getters
  getCurrentSession: () => ChatSession | null;
  getSessionById: (sessionId: string) => ChatSession | null;
  getSortedSessions: () => ChatSession[];
}

export const useHistoryStore = create<HistoryState>((set, get) => ({
  sessions: [],
  currentSessionId: null,
  isLoading: false,
  error: null,

  loadHistory: async () => {
    set({ isLoading: true, error: null });
    try {
      // Check if we're in a CEP environment
      const cs = (window as any).CSInterface;
      if (!cs) {
        // Fallback to localStorage for development/testing
        const stored = localStorage.getItem(HISTORY_STORAGE_KEY);
        const sessions = stored ? JSON.parse(stored) : [];
        set({ sessions, isLoading: false });
        return;
      }

      const result = await executeExtendScript('loadHistory()');
      if (result && result.success && result.data) {
        const sessions = Array.isArray(result.data) ? result.data : [];
        set({ sessions, isLoading: false });
      } else if (result && result.success) {
        // Empty but successful response
        set({ sessions: [], isLoading: false });
      } else {
        throw new Error(result?.message || 'Failed to load history from ExtendScript');
      }
    } catch (error: any) {
      console.error('Failed to load history:', error);

      // Fallback to localStorage
      try {
        const stored = localStorage.getItem(HISTORY_STORAGE_KEY);
        const sessions = stored ? JSON.parse(stored) : [];
        set({
          sessions,
          isLoading: false,
          error: `Using local storage fallback: ${error.message}`
        });
      } catch (fallbackError) {
        set({
          error: error.message || 'Failed to load chat history',
          isLoading: false,
          sessions: []
        });
      }
    }
  },

  saveSession: async (session: ChatSession) => {
    try {
      // Update local state first
      set(state => ({
        sessions: state.sessions.some(s => s.id === session.id)
          ? state.sessions.map(s => s.id === session.id ? session : s)
          : [...state.sessions, session]
      }));

      const allSessions = get().sessions;

      // Check if we're in a CEP environment
      const cs = (window as any).CSInterface;
      if (!cs) {
        // Fallback to localStorage for development/testing
        localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(allSessions));
        return;
      }

      // Save to ExtendScript
      await executeExtendScript(`saveHistory(${JSON.stringify(allSessions)})`);

      // Also save to localStorage as backup
      localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(allSessions));
    } catch (error: any) {
      console.error('Failed to save session:', error);

      // Try localStorage fallback
      try {
        const allSessions = get().sessions;
        localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(allSessions));
      } catch (fallbackError) {
        set({ error: error.message || 'Failed to save session' });
      }
    }
  },

  deleteSession: async (sessionId: string) => {
    try {
      // Update local state
      set(state => ({
        sessions: state.sessions.filter(s => s.id !== sessionId),
        currentSessionId: state.currentSessionId === sessionId ? null : state.currentSessionId
      }));

      const allSessions = get().sessions;

      // Check if we're in a CEP environment
      const cs = (window as any).CSInterface;
      if (!cs) {
        // Fallback to localStorage for development/testing
        localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(allSessions));
        return;
      }

      // Save updated sessions to ExtendScript
      await executeExtendScript(`saveHistory(${JSON.stringify(allSessions)})`);

      // Also save to localStorage as backup
      localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(allSessions));
    } catch (error: any) {
      console.error('Failed to delete session:', error);

      // Try localStorage fallback
      try {
        const allSessions = get().sessions;
        localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(allSessions));
      } catch (fallbackError) {
        set({ error: error.message || 'Failed to delete session' });
      }
    }
  },

  clearHistory: async () => {
    try {
      set({ sessions: [], currentSessionId: null });

      // Check if we're in a CEP environment
      const cs = (window as any).CSInterface;
      if (!cs) {
        // Fallback to localStorage for development/testing
        localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify([]));
        return;
      }

      await executeExtendScript('saveHistory([])');

      // Also clear localStorage
      localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify([]));
    } catch (error: any) {
      console.error('Failed to clear history:', error);

      // Try localStorage fallback
      try {
        localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify([]));
      } catch (fallbackError) {
        set({ error: error.message || 'Failed to clear history' });
      }
    }
  },

  createSession: (title?: string) => {
    const session: ChatSession = {
      id: crypto.randomUUID(),
      title: title || `Chat ${new Date().toLocaleDateString()}`,
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    set(state => ({
      sessions: [session, ...state.sessions],
      currentSessionId: session.id
    }));

    // Save the new session
    const { saveSession } = get();
    saveSession(session);

    return session;
  },

  updateSession: (sessionId: string, updates: Partial<ChatSession>) => {
    set(state => ({
      sessions: state.sessions.map(session =>
        session.id === sessionId
          ? { ...session, ...updates, updatedAt: Date.now() }
          : session
      )
    }));
  },

  setCurrentSession: (sessionId: string | null) => {
    set({ currentSessionId: sessionId });
  },

  getCurrentSession: () => {
    const { sessions, currentSessionId } = get();
    return sessions.find(s => s.id === currentSessionId) || null;
  },

  getSessionById: (sessionId: string) => {
    const { sessions } = get();
    return sessions.find(s => s.id === sessionId) || null;
  },

  getSortedSessions: () => {
    const { sessions } = get();
    return [...sessions].sort((a, b) => b.updatedAt - a.updatedAt);
  },
}));
