import { create } from 'zustand';

export type Message = {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
};

interface ChatState {
  messages: Message[];
  isLoading: boolean;
  currentSession?: string;
  addMessage: (msg: Omit<Message, 'id' | 'timestamp'>) => void;
  setLoading: (val: boolean) => void;
  createNewSession: () => void;
  loadSession: (sessionId: string, messages: Message[]) => void;
  clearMessages: () => void;
}

export const useChatStore = create<ChatState>((set, get) => ({
  messages: [],
  isLoading: false,

  addMessage: (msg) => {
    const newMessage = { ...msg, id: crypto.randomUUID(), timestamp: Date.now() };
    set((state) => ({
      messages: [...state.messages, newMessage],
    }));

    // Auto-save to history if we have a current session
    const currentSession = get().currentSession;
    if (currentSession) {
      // Use a timeout to ensure the message is added to the store before saving
      setTimeout(() => {
        // Import historyStore dynamically to avoid circular dependency
        import('./historyStore')
          .then(({ useHistoryStore }) => {
            const historyStore = useHistoryStore.getState();
            const session = historyStore.sessions.find(s => s.id === currentSession);
            if (session) {
              const updatedSession = {
                ...session,
                messages: [...get().messages],
                updatedAt: Date.now(),
                title: session.title === `Chat ${new Date(session.createdAt).toLocaleDateString()}`
                  ? (get().messages[0]?.content.slice(0, 50) + '...' || session.title)
                  : session.title
              };
              historyStore.saveSession(updatedSession);
            }
          })
          .catch(error => {
            console.error('Failed to import history store:', error);
          });
      }, 0);
    }
  },

  setLoading: (val) => set({ isLoading: val }),

  createNewSession: () => {
    const sessionId = crypto.randomUUID();
    set({
      messages: [],
      currentSession: sessionId
    });

    // Create session in history store
    import('./historyStore')
      .then(({ useHistoryStore }) => {
        const historyStore = useHistoryStore.getState();
        historyStore.createSession();
      })
      .catch(error => {
        console.error('Failed to import history store:', error);
      });

    return sessionId;
  },

  loadSession: (sessionId: string, messages: Message[]) => {
    set({
      currentSession: sessionId,
      messages: messages
    });
  },

  clearMessages: () => {
    set({ messages: [], currentSession: undefined });
  }
}));
