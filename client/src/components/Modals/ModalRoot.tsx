import React from 'react';
import { useModalStore } from '../stores/modalStore';
import { ProviderModal } from './ProviderModal';
import { SettingsModal } from './SettingsModal';
import { ChatHistoryModal } from './ChatHistoryModal';
import { StatusModal } from './StatusModal';

export const ModalRoot: React.FC = () => {
  const { modal } = useModalStore();

  if (!modal) return null;

  switch (modal) {
    case 'provider':
      return <ProviderModal />;
    case 'settings':
      return <SettingsModal />;
    case 'chat-history':
      return <ChatHistoryModal />;
    case 'status':
      return <StatusModal />;
    default:
      return null;
  }
};
