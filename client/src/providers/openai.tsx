import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { SearchableModelSelect } from '../components/ui/SearchableModelSelect';
import { ProviderBridge } from '../utils/cepIntegration';
import { Loader2 } from 'lucide-react';

interface Props {
  onSave: (config: { apiKey: string; selectedModelId: string }) => void;
}

export const OpenAIProvider: React.FC<Props> = ({ onSave }) => {
  const { getActiveProvider } = useSettingsStore();
  const [apiKey, setApiKey] = useState(getActiveProvider()?.apiKey || '');
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState(getActiveProvider()?.selectedModelId || '');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!apiKey) return;

    setLoading(true);
    setError('');
    ProviderBridge.listModels('openai', 'https://api.openai.com/v1', apiKey)
      .then((models: any) => {
        setModels(models as { id: string; name: string }[]);
        setError('');
      })
      .catch((e: any) => setError('Failed to load models: ' + e.message))
      .finally(() => setLoading(false));
  }, [apiKey]);

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  return (
    <div className="space-y-4">
      {/* API Key Section */}
      <div>
        <label className="block text-sm font-medium text-adobe-text-primary mb-2">
          API Key
        </label>
        <input
          type="password"
          placeholder="sk-..."
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary"
        />
      </div>

      {/* Model Selection */}
      <div>
        <label className="block text-sm font-medium text-adobe-text-primary mb-2">
          Model
        </label>
        {loading ? (
          <div className="flex items-center space-x-2 text-adobe-text-secondary">
            <Loader2 size={16} className="animate-spin" />
            <span>Loading models...</span>
          </div>
        ) : error ? (
          <div className="space-y-2">
            <p className="text-adobe-error text-sm">{error}</p>
            <button
              onClick={() => {
                if (!apiKey) return;
                setLoading(true);
                setError('');
                ProviderBridge.listModels('openai', 'https://api.openai.com/v1', apiKey)
                  .then((models: any) => {
                    setModels(models as { id: string; name: string }[]);
                    setError('');
                  })
                  .catch((e: any) => setError('Failed to load models: ' + e.message))
                  .finally(() => setLoading(false));
              }}
              className="text-adobe-accent text-sm hover:underline"
            >
              Retry
            </button>
          </div>
        ) : models.length === 0 ? (
          <div className="space-y-2">
            <p className="text-adobe-warning text-sm">No models found. Check config or retry.</p>
            <p className="text-adobe-text-secondary text-xs">
              Verify your API key has access to OpenAI models.
            </p>
            <button
              onClick={() => {
                if (!apiKey) return;
                setLoading(true);
                setError('');
                ProviderBridge.listModels('openai', 'https://api.openai.com/v1', apiKey)
                  .then((models: any) => {
                    setModels(models as { id: string; name: string }[]);
                    setError('');
                  })
                  .catch((e: any) => setError('Failed to load models: ' + e.message))
                  .finally(() => setLoading(false));
              }}
              className="text-adobe-accent text-sm hover:underline"
            >
              Retry
            </button>
          </div>
        ) : (
          <SearchableModelSelect
            models={models}
            value={selectedModel}
            onChange={handleModelChange}
            placeholder="Search OpenAI models..."
          />
        )}
      </div>

      {/* Save Button */}
      <button
        onClick={() => onSave({ apiKey, selectedModelId: selectedModel })}
        disabled={!apiKey || !selectedModel}
        className="w-full bg-adobe-accent text-white rounded-md py-3 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
      >
        Save Configuration
      </button>
    </div>
  );
};
