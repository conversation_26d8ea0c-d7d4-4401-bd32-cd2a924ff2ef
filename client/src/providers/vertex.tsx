import React, { useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const VertexProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [projectId, setProjectId] = useState('');
  const [location, setLocation] = useState('us-central1');
  const models = [
    { id: 'gemini-pro', name: 'Gemini Pro' },
    { id: 'gemini-pro-vision', name: 'Gemini Pro Vision' },
  ];

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('vertex', key, e.target.value)}
        >
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Project ID
        </label>
        <input
          type="text"
          placeholder="your-project-id"
          value={projectId}
          onChange={(e) => setProjectId(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Location
        </label>
        <input
          type="text"
          placeholder="us-central1"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Service Account Key
        </label>
        <textarea
          placeholder="Paste your service account JSON key here..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1 h-20 text-xs"
        />
      </div>
      <button
        onClick={() => { updateProviderKey('vertex', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
