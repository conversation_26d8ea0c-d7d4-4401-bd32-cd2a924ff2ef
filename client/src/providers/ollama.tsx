import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { ProviderBridge } from '../utils/cepIntegration';

export const OllamaProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [url, setUrl] = useState('http://localhost:11434');
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState('');

  useEffect(() => {
    ProviderBridge.listModels('ollama', url, '')
      .then((models: any) => setModels(models as { id: string; name: string }[]));
  }, [url]);

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Ollama URL
        </label>
        <input
          type="text"
          placeholder="http://localhost:11434"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => setSelectedModel(e.target.value)}
        >
          <option value="">Select a model...</option>
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>
      <button
        onClick={() => { updateProviderKey('ollama', url, selectedModel); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
