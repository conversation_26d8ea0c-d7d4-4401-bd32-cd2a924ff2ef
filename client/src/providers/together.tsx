import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

export const TogetherProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);

  useEffect(() => {
    fetch('https://api.together.xyz/v1/models', {
      headers: { Authorization: `Bearer ${key}` }
    })
      .then(res => res.json())
      .then(data => setModels(data.data || []))
      .catch(() => setModels([]));
  }, [key]);

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        <select
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
          onChange={(e) => updateProviderKey('together', key, e.target.value)}
        >
          <option value="">Select a model...</option>
          {models.map((m) => (
            <option key={m.id} value={m.id}>{m.name}</option>
          ))}
        </select>
      </div>
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>
      <button
        onClick={() => { updateProviderKey('together', key); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
