import { create } from 'zustand';
import { CEPSettings } from '../utils/cepIntegration';
import { toast } from '../components/stores/toastStore';

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities?: string[];
  isRecommended?: boolean;
}

interface Provider {
  id: string;
  name: string;
  isConfigured: boolean;
  configType: 'apiKey' | 'baseURL' | 'other';
  apiKey?: string;
  baseURL?: string;
  models: Model[];
  selectedModelId?: string;
  isLoading?: boolean;
  error?: string;
  settings?: Record<string, unknown>;
}

interface SettingsState {
  providers: Provider[];
  activeProviderId: string | undefined;
  isLoadingModels: boolean;

  // Provider actions
  setActiveProvider: (providerId: string) => void;
  updateProviderConfig: (providerId: string, config: Partial<Provider>) => void;
  setProviderModels: (providerId: string, models: Model[]) => void;
  setSelectedModel: (providerId: string, modelId: string) => void;
  updateProviderKey: (providerId: string, apiKey: string, selectedModelId?: string) => void;
  saveProviderSelection: (providerId: string, config: Partial<Provider>) => void;

  // Model actions
  loadModelsForProvider: (providerId: string) => Promise<void>;

  // CEP integration
  persistSettings: () => void;
  loadSettings: () => Promise<void>;

  // Computed getters
  getActiveProvider: () => Provider | null;
  getActiveModel: () => Model | null;
}

export const useSettingsStore = create<SettingsState>((set, get) => ({
  providers: [
    { id: 'openai', name: 'OpenAI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'anthropic', name: 'Anthropic', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'gemini', name: 'Google Gemini', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'groq', name: 'Groq', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'deepseek', name: 'DeepSeek', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'mistral', name: 'Mistral', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'moonshot', name: 'Moonshot AI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'openrouter', name: 'OpenRouter', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'perplexity', name: 'Perplexity', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'qwen', name: 'Alibaba Qwen', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'together', name: 'Together AI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'vertex', name: 'Google Vertex AI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'xai', name: 'xAI', configType: 'apiKey', isConfigured: false, models: [] },
    { id: 'ollama', name: 'Ollama', configType: 'baseURL', isConfigured: false, models: [] },
    { id: 'lmstudio', name: 'LM Studio', configType: 'baseURL', isConfigured: false, models: [] },
  ],
  activeProviderId: undefined,
  isLoadingModels: false,

  setActiveProvider: (providerId) => {
    set({ activeProviderId: providerId });
    get().persistSettings();
  },

  updateProviderConfig: (providerId, config) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, ...config, isConfigured: !!(config.apiKey || config.baseURL) } : p
      )
    }));
    get().persistSettings();
    
    // Load models after configuration
    if (config.apiKey || config.baseURL) {
      get().loadModelsForProvider(providerId);
    }
  },

  setProviderModels: (providerId, models) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, models, isLoading: false, error: undefined } : p
      )
    }));
  },

  setSelectedModel: (providerId, modelId) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, selectedModelId: modelId } : p
      )
    }));
    get().persistSettings();
  },

  updateProviderKey: (providerId, apiKey, selectedModelId) => {
    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, apiKey, isConfigured: !!apiKey, selectedModelId: selectedModelId || p.selectedModelId }
          : p
      )
    }));
    get().persistSettings();
    
    // Load models after configuration
    if (apiKey) {
      get().loadModelsForProvider(providerId);
    }
  },

  saveProviderSelection: (providerId, config) => {
    const provider = get().providers.find(p => p.id === providerId);

    set(state => ({
      activeProviderId: providerId,
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, ...config, isConfigured: !!(config.apiKey || config.baseURL) }
          : p
      )
    }));
    get().persistSettings();

    // Show success toast
    toast.success(
      'Provider configured',
      `${provider?.name || providerId} has been configured successfully`,
      3000
    );

    // Load models after configuration
    if (config.apiKey || config.baseURL) {
      get().loadModelsForProvider(providerId);
    }
  },

  loadModelsForProvider: async (providerId) => {
    const provider = get().providers.find(p => p.id === providerId);
    if (!provider) return;

    // Check if provider has required configuration based on configType
    if (provider.configType === 'baseURL' && !provider.baseURL) {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, error: 'Base URL required' } : p
        )
      }));
      return;
    } else if (provider.configType === 'apiKey' && !provider.apiKey) {
      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, error: 'API Key required' } : p
        )
      }));
      return;
    }

    console.log(`Loading models for provider: ${providerId}`);

    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId ? { ...p, isLoading: true, error: undefined } : p
      )
    }));

    try {
      // Use CEP bridge to get models
      const { ProviderBridge } = await import('../utils/cepIntegration');
      const models = await ProviderBridge.listModels(
        providerId,
        provider.baseURL,
        provider.apiKey
      );

      console.log(`Received ${models.length} models for ${providerId}:`, models);

      // Transform to Model interface with fallback for incomplete data
      const transformedModels: Model[] = (models as any[]).map((m: any) => ({
        id: m.id || 'unknown-id',  // Fallback if id missing
        name: m.name || m.id || 'Unknown Model',  // Use id as name if missing
        description: m.description || '',  // Existing
        contextLength: m.contextLength || 4096,  // Existing
        isRecommended: m.isRecommended || false  // Existing
      }));

      console.log(`Transformed models for ${providerId}:`, transformedModels);

      get().setProviderModels(providerId, transformedModels);  // Set immediately for dynamic menu update

      // Show success toast
      toast.success(
        'Models loaded successfully',
        `Found ${transformedModels.length} models for ${provider.name}`,
        3000
      );
    } catch (error: any) {
      console.error(`Error loading models for ${providerId}:`, error);

      const errorMessage = error?.message || String(error);
      const friendlyError = errorMessage.includes('timeout')
        ? 'Request timed out. Please check your internet connection and try again.'
        : errorMessage.includes('network')
        ? 'Network error. Please check your internet connection.'
        : errorMessage.includes('unauthorized') || errorMessage.includes('401')
        ? 'Invalid API key. Please check your credentials.'
        : errorMessage.includes('forbidden') || errorMessage.includes('403')
        ? 'Access denied. Please check your API key permissions.'
        : errorMessage.includes('not found') || errorMessage.includes('404')
        ? 'API endpoint not found. Please check the provider configuration.'
        : errorMessage;

      set(state => ({
        providers: state.providers.map(p =>
          p.id === providerId ? { ...p, isLoading: false, error: friendlyError } : p
        )
      }));

      // Show error toast
      toast.error(
        'Failed to load models',
        `${provider.name}: ${friendlyError}`,
        5000
      );
    }
  },

  persistSettings: () => {
    const { activeProviderId, providers } = get();
    CEPSettings.save({
      activeProviderId,
      providers: providers.map(p => ({
        id: p.id,
        isConfigured: p.isConfigured,
        configType: p.configType,
        apiKey: p.apiKey,
        baseURL: p.baseURL,
        selectedModelId: p.selectedModelId,
        settings: p.settings
      }))
    });
  },

  loadSettings: async () => {
    try {
      const settings = await CEPSettings.load();
      if (settings.activeProviderId) {
        set({ activeProviderId: settings.activeProviderId });
      }
      if (settings.providers && Array.isArray(settings.providers)) {
        set(state => ({
          providers: state.providers.map(p => {
            const saved = settings.providers?.find(sp => sp.id === p.id);
            return saved ? { ...p, ...saved } : p;
          })
        }));
      }

      // Load models for configured providers
      const state = get();
      state.providers.forEach(provider => {
        if (provider.isConfigured && (provider.apiKey || provider.baseURL)) {
          get().loadModelsForProvider(provider.id);
        }
      });
    } catch (error) {
      console.error('Failed to load CEP settings:', error);
    }
  },

  getActiveProvider: () => {
    const { providers, activeProviderId } = get();
    return providers.find(p => p.id === activeProviderId) || null;
  },

  getActiveModel: () => {
    const activeProvider = get().getActiveProvider();
    if (!activeProvider?.selectedModelId) return null;
    return activeProvider.models.find(m => m.id === activeProvider.selectedModelId) || null;
  }
}));
