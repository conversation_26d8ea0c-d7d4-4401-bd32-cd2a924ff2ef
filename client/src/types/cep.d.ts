export interface CSInterface {
  // Core methods
  getApplicationID(): string;
  getApplicationVersion(): string;
  getSystemPath(pathType: string): string;
  getExtensionID(): string;
  getHostEnvironment(): HostEnvironment;
  closeExtension(): void;
  getMonitorScaleFactor(): number;
  setScaleFactorChangedHandler(handler: (scaleFactor: number) => void): void;
  
  // Event methods
  addEventListener(eventType: string, handler: (event: CSEvent) => void): void;
  removeEventListener(eventType: string, handler: (event: CSEvent) => void): void;
  dispatchEvent(event: CSEvent): void;
  
  // EvalScript methods
  evalScript(script: string, callback?: (result: string) => void): void;
  
  // UI methods
  openURLInDefaultBrowser(url: string): void;
}

export interface HostEnvironment {
  appId: string;
  appLocale: string;
  appVersion: string;
  appUILocale: string;
  extensionId: string;
  extensionVersion: string;
  appName: string;
  isPreRelease: boolean;
}

export interface CSEvent {
  type: string;
  scope: string;
  appId: string;
  extensionId: string;
  data: any;
}

export interface ExtendScriptResponse {
  success: boolean;
  message?: string;
  data?: any;
}

export interface AppInfo {
  name: string;
  version: string;
  locale: string;
  build: string;
}

export interface DocumentInfo {
  name: string;
  path: string;
  saved: boolean;
  modified: boolean;
}

export interface SystemInfo {
  os: string;
  version: string;
  buildDate: string;
  locale: string;
  memoryUsage: number;
}

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  isRecommended?: boolean;
}

export interface ProviderConfig {
  id: string;
  name: string;
  isConfigured: boolean;
  configType: 'apiKey' | 'baseURL' | 'other';
  apiKey?: string;
  baseURL?: string;
  models: Model[];
  selectedModelId?: string;
  isLoading?: boolean;
  error?: string;
  settings?: Record<string, unknown>;
}
