export const DEFAULT_TIMEOUT = 30000;
export const DEFAULT_RETRIES = 2;
export const DEFAULT_MODEL_TIMEOUT = 10000;
export const DEFAULT_MODEL_RETRIES = 3;
export const MAX_RETRY_DELAY = 3000;
export const BASE_RETRY_DELAY = 1000;

// Provider-specific timeouts
export const LOCAL_PROVIDER_TIMEOUT = 8000;
export const API_PROVIDER_TIMEOUT = 12000;

// API endpoints
export const OPENAI_API_BASE = 'https://api.openai.com/v1';
export const ANTHROPIC_API_BASE = 'https://api.anthropic.com/v1';
export const GEMINI_API_BASE = 'https://generativelanguage.googleapis.com/v1beta';
export const GROQ_API_BASE = 'https://api.groq.com/openai/v1';
export const DEEPSEEK_API_BASE = 'https://api.deepseek.com/v1';
export const MISTRAL_API_BASE = 'https://api.mistral.ai/v1';
export const MOONSHOT_API_BASE = 'https://api.moonshot.cn/v1';
export const OPENROUTER_API_BASE = 'https://openrouter.ai/api/v1';
export const PERPLEXITY_API_BASE = 'https://api.perplexity.ai';
export const QWEN_API_BASE = 'https://dashscope.aliyuncs.com/api/v1';
export const TOGETHER_API_BASE = 'https://api.together.xyz/v1';
export const VERTEX_API_BASE = 'https://generativelanguage.googleapis.com/v1beta';
export const XAI_API_BASE = 'https://api.x.ai/v1';

// Local provider defaults
export const OLLAMA_DEFAULT_URL = 'http://localhost:11434';
export const LMSTUDIO_DEFAULT_URL = 'http://localhost:1234';

// Storage keys
export const SETTINGS_STORAGE_KEY = 'sahAI_settings';
export const HISTORY_STORAGE_KEY = 'sahai-chat-history';

// File paths
export const SETTINGS_FILE_PATH = '~/Adobe/CEP/extensions/SahAI/settings.json';
export const HISTORY_FILE_PATH = '~/Adobe/CEP/extensions/SahAI/history.json';
