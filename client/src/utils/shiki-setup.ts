// Optimized Shiki setup for CEP extension
// Uses dynamic imports to load only essential languages on demand

import { createHighlighter } from 'shiki';
import type { Highlighter, BundledLanguage, BundledTheme } from 'shiki';

// Define only the essential languages we need for Adobe CEP workflows
const ESSENTIAL_LANGUAGES: BundledLanguage[] = [
  // Core Web/CEP Technologies
  'javascript',
  'typescript',
  'jsx',
  'tsx',
  'html',
  'css',
  'scss',
  'less',

  // Configuration Files
  'json',
  'jsonc', // JSON with comments (Adobe configs)
  'xml',
  'yaml',

  // Documentation
  'markdown',

  // Common Development Languages
  'python',
  'swift',
  'rust',
  'go',
  'java',
  'php',
  'ruby',
  'shell',

  // Adobe-Specific Languages
  'actionscript-3'  // Flash/Animate projects
];

const ESSENTIAL_THEMES: BundledTheme[] = ['github-dark'];

/**
 * A promise that resolves to a configured <PERSON>ki highlighter.
 * Uses only essential languages for Adobe CEP workflows to reduce bundle size.
 */
export const highlighterPromise = createHighlighter({
  themes: ESSENTIAL_THEMES,
  langs: ESSENTIAL_LANGUAGES
});

/**
 * Helper to get the highlighter in async/await style:
 * 
 * Usage:
 *   import { highlighterPromise } from './utils/shiki-setup'
 *   const highlighter = await highlighterPromise
 *   const html = highlighter.codeToHtml(code, { lang: 'typescript' })
 */

/**
 * Cached highlighter instance to avoid repeated initialization
 */
let cachedHighlighter: Highlighter | null = null;

/**
 * Get the highlighter instance (cached after first load)
 */
export async function getOptimizedHighlighter(): Promise<Highlighter> {
  if (!cachedHighlighter) {
    cachedHighlighter = await highlighterPromise;
  }
  return cachedHighlighter;
}

/**
 * Check if a language is supported by our optimized setup
 */
export function isSupportedLanguage(lang: string): boolean {
  const supportedLangs = [
    'javascript', 'typescript', 'jsx', 'tsx',
    'html', 'css', 'scss', 'less',
    'json', 'jsonc', 'xml', 'yaml',
    'markdown',
    'python', 'swift', 'rust', 'go', 'java', 'php', 'ruby', 'shell',
    'actionscript-3', 'actionscript'
  ];

  return supportedLangs.includes(lang.toLowerCase());
}

/**
 * Get fallback language for unsupported languages
 */
export function getFallbackLanguage(lang: string): string {
  // Map common aliases to supported languages
  const aliasMap: Record<string, string> = {
    'js': 'javascript',
    'ts': 'typescript',
    'bash': 'shell',
    'sh': 'shell',
    'zsh': 'shell',
    'fish': 'shell',
    'py': 'python',
    'rb': 'ruby',
    'yml': 'yaml',
    'htm': 'html',
    'sass': 'scss',
    'jsx': 'jsx',
    'tsx': 'tsx'
  };
  
  const normalizedLang = lang.toLowerCase();
  
  // Return mapped language if available
  if (aliasMap[normalizedLang]) {
    return aliasMap[normalizedLang];
  }
  
  // Return original if supported
  if (isSupportedLanguage(normalizedLang)) {
    return normalizedLang;
  }
  
  // Default fallback
  return 'text';
}
