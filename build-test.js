#!/usr/bin/env node
import { execSync } from 'child_process';
import fs from 'fs';

console.log('Starting build test...');

try {
  console.log('Running vite build...');
  const output = execSync('npx vite build', { 
    encoding: 'utf8',
    stdio: 'inherit'
  });
  console.log('Build completed!');
  
  // Check if dist directory exists
  if (fs.existsSync('dist')) {
    console.log('Dist directory created');
    const files = fs.readdirSync('dist', { recursive: true });
    console.log('Files in dist:', files);
  } else {
    console.log('No dist directory found');
  }
  
} catch (error) {
  console.error('Build failed:', error.message);
  process.exit(1);
}
